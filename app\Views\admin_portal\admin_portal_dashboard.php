<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    /* Scoped styles for admin dashboard quick action buttons */
    .admin-dashboard .quick-actions .btn {
        min-height: 72px; /* Larger touch target */
        padding: 14px 16px;
        border-radius: 14px; /* Square-ish app-like buttons */
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        white-space: normal;
    }

    .admin-dashboard .quick-actions .btn i {
        font-size: 1.25rem;
        margin-right: 0.5rem;
    }

    @media (max-width: 576px) {
        .admin-dashboard .quick-actions .btn {
            min-height: 84px; /* Even larger on phones */
            border-radius: 16px;
            padding: 16px 18px;
            font-size: 1rem;
        }
        .admin-dashboard .quick-actions .btn i {
            font-size: 1.35rem;
        }
    }
    /* Improve visibility of outline buttons inside gradient card headers */
    .admin-dashboard .card-header .btn.btn-outline-primary,
    .admin-dashboard .card-header .btn.btn-outline-success,
    .admin-dashboard .card-header .btn.btn-outline-info,
    .admin-dashboard .card-header .btn.btn-outline-warning,
    .admin-dashboard .card-header .btn.btn-outline-danger,
    .admin-dashboard .card-header .btn.btn-outline-secondary {
        color: #fff;
        border-color: #fff;
    }
    .admin-dashboard .card-header .btn.btn-outline-primary:hover,
    .admin-dashboard .card-header .btn.btn-outline-success:hover,
    .admin-dashboard .card-header .btn.btn-outline-info:hover,
    .admin-dashboard .card-header .btn.btn-outline-warning:hover,
    .admin-dashboard .card-header .btn.btn-outline-danger:hover,
    .admin-dashboard .card-header .btn.btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.18);
        color: #fff;
        border-color: #fff;
    }
</style>
<?= $this->endSection() ?>


<?= $this->section('content') ?>
<div class="admin-dashboard">

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h3 mb-2 text-primary">
                            <i class="bi bi-speedometer2 me-3"></i>
                            Welcome to Admin Portal
                        </h1>
                        <p class="text-muted mb-0">
                            Hello <strong><?= $user['name'] ?></strong>!
                            You are logged in as
                            <?php if ($user['is_admin']): ?>
                                <span class="badge bg-primary">Administrator</span>
                            <?php endif; ?>
                            <?php if ($user['is_supervisor']): ?>
                                <span class="badge bg-info">Supervisor</span>
                            <?php endif; ?>
                        </p>
                        <p class="text-muted small mt-1">
                            <i class="bi bi-envelope me-1"></i><?= $user['email'] ?>
                        </p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="d-flex align-items-center justify-content-md-end">
                            <div class="me-3">
                                <small class="text-muted d-block">Last Login</small>
                                <strong><?= date('M d, Y H:i') ?></strong>
                            </div>
                            <a href="<?= base_url('admin/profile') ?>" class="text-decoration-none" title="Open My Profile">
                                <div class="stats-icon" style="background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)); width: 60px; height: 60px;">
                                    <i class="bi bi-person-check" style="font-size: 1.5rem;"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                <i class="bi bi-calendar-check"></i>
            </div>
            <div class="stats-number"><?= $stats['total_workplans'] ?? 0 ?></div>
            <div class="stats-label">Total Workplans</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #007bff, #6610f2);">
                <i class="bi bi-activity"></i>
            </div>
            <div class="stats-number"><?= $stats['total_activities'] ?? 0 ?></div>
            <div class="stats-label">Total Activities</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                <i class="bi bi-upload"></i>
            </div>
            <div class="stats-number"><?= $stats['submitted_activities'] ?? 0 ?></div>
            <div class="stats-label">Submitted Activities</div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="stats-icon" style="background: linear-gradient(45deg, #17a2b8, #6f42c1);">
                <i class="bi bi-check-circle-fill"></i>
            </div>
            <div class="stats-number"><?= $stats['approved_activities'] ?? 0 ?></div>
            <div class="stats-label">Approved Activities</div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-8 mb-4">

        <!-- Project Management Card -->
        <div class="card mb-3">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="bi bi-calendar-check me-2"></i>Project Management
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3 quick-actions">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/workplans') ?>" class="btn btn-outline-primary">
                                <i class="bi bi-calendar-check me-2"></i>
                                Workplans
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/activities') ?>" class="btn btn-outline-primary">
                                <i class="bi bi-activity me-2"></i>
                                Activities
                            </a>
                        </div>
                    </div>
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="<?= base_url('field/dashboard') ?>" class="btn btn-outline-primary">
                                    <i class="bi bi-geo-alt me-2"></i>
                                    Field Dashboard
                                </a>
                            </div>
                        </div>

                </div>
            </div>
        </div>

        <!-- Goods Management Card -->
        <div class="card mb-3">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="bi bi-box-seam me-2"></i>Goods Management
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3 quick-actions">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/goods-groups') ?>" class="btn btn-outline-success">
                                <i class="bi bi-collection me-2"></i>
                                Goods Groups
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/goods-brands') ?>" class="btn btn-outline-success">
                                <i class="bi bi-tags me-2"></i>
                                Goods Brands
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/goods-items') ?>" class="btn btn-outline-success">
                                <i class="bi bi-box me-2"></i>
                                Goods Items
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/goods-report') ?>" class="btn btn-outline-success">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                Goods Report
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Management Card -->
        <div class="card mb-3">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-building me-2"></i>Business Management
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3 quick-actions">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/business-entities') ?>" class="btn btn-outline-info">
                                <i class="bi bi-building me-2"></i>
                                Business Entities
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/business-locations') ?>" class="btn btn-outline-info">
                                <i class="bi bi-geo-alt me-2"></i>
                                Business Locations
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/business-entities-report') ?>" class="btn btn-outline-info">
                                <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                                Business Entities Report
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Management Card -->
        <div class="card mb-3">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="bi bi-people me-2"></i>User Management
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3 quick-actions">
                    <div class="col-md-6">
                        <div class="d-grid">
                            <a href="<?= base_url('admin/users') ?>" class="btn btn-outline-warning">
                                <i class="bi bi-person-plus me-2"></i>
                                Manage Users
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>


    <!-- System Information -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-info-circle me-2"></i>System Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted d-block">System Version</small>
                    <strong>PCOLLX v1.0.0</strong>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block">Database Status</small>
                    <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>Connected
                    </span>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block">Server Status</small>
                    <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>Online
                    </span>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block">Last Backup</small>
                    <strong><?= date('M d, Y') ?></strong>
                </div>
                <hr>
                <div class="text-center">
                    <button class="btn btn-primary-custom btn-sm" onclick="alert('Feature coming soon!')">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Refresh Status
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Workplans and Activities -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>Recent Workplans</h5>
                <a href="<?= base_url('admin/workplans') ?>" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (isset($recent_workplans) && !empty($recent_workplans)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_workplans as $workplan): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold"><?= esc($workplan['title']) ?></div>
                                    <small class="text-muted">
                                        <?= date('M d, Y', strtotime($workplan['date_from'])) ?> -
                                        <?= date('M d, Y', strtotime($workplan['date_to'])) ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?= $workplan['status'] === 'active' ? 'success' : 'secondary' ?> rounded-pill">
                                    <?= ucfirst($workplan['status']) ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2 mb-0">No workplans found</p>
                        <a href="<?= base_url('admin/workplans/new') ?>" class="btn btn-sm btn-primary mt-2">
                            <i class="bi bi-plus-circle me-1"></i>Create First Workplan
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-activity me-2"></i>Recent Activities</h5>
                <a href="<?= base_url('admin/activities') ?>" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (isset($recent_activities) && !empty($recent_activities)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_activities as $activity): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold"><?= esc($activity['activity_name']) ?></div>
                                    <small class="text-muted">
                                        <?= ucwords(str_replace('_', ' ', $activity['activity_type'])) ?> •
                                        <?= date('M d, Y', strtotime($activity['date_from'])) ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?=
                                    $activity['status'] === 'active' ? 'success' :
                                    ($activity['status'] === 'submitted' ? 'info' :
                                    ($activity['status'] === 'approved' ? 'primary' : 'secondary'))
                                ?> rounded-pill">
                                    <?= ucfirst($activity['status']) ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-activity text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2 mb-0">No activities found</p>
                        <?php if (isset($stats) && $stats['total_workplans'] > 0): ?>
                            <a href="<?= base_url('admin/activities/new') ?>" class="btn btn-sm btn-primary mt-2">
                                <i class="bi bi-plus-circle me-1"></i>Create First Activity
                            </a>
                        <?php else: ?>
                            <p class="text-muted small mt-1">Create a workplan first to add activities</p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Welcome message
    document.addEventListener('DOMContentLoaded', function() {
        // Show success message if redirected from login
        <?php if (session()->getFlashdata('success')): ?>
            // You can add a toast notification here if needed
            console.log('<?= session()->getFlashdata('success') ?>');
        <?php endif; ?>

        // Auto-refresh stats every 30 seconds (when implemented)
        // setInterval(refreshStats, 30000);
    });

    function refreshStats() {
        // This will be implemented when we have actual data to display
        console.log('Refreshing statistics...');
    }

    // Quick action handlers
    function manageUsers() {
        alert('User management feature will be implemented in the next phase.');
    }

    function manageOrganizations() {
        alert('Organization management feature will be implemented in the next phase.');
    }

    function viewReports() {
        alert('Reports feature will be implemented in the next phase.');
    }

    function systemSettings() {
        alert('System settings feature will be implemented in the next phase.');
    }
</script>
<?= $this->endSection() ?>
